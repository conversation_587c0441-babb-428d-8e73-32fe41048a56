#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
文本清理工具函数
提供各种文本预处理和清理功能
"""

import re
import string
from typing import List, Dict, Tuple

class TextCleaner:
    """文本清理工具类"""
    
    def __init__(self):
        """初始化文本清理器"""
        # OCR常见错误映射
        self.ocr_corrections = {
            # 数字与字母混淆
            '0': 'O', '1': 'l', '5': 'S', '6': 'G', '8': 'B',
            # 中文标点符号标准化
            '，': '，', '。': '。', '；': '；', '：': '：',
            '？': '？', '！': '！', '"': '"', '"': '"',
            ''': ''', ''': ''', '（': '（', '）': '）'
        }
        
        # 噪声模式
        self.noise_patterns = [
            r'^\d+$',  # 纯数字行（页码）
            r'^第\s*\d+\s*页',  # 页码信息
            r'^\s*[-=_]{3,}\s*$',  # 分隔线
            r'^\s*[\.]{3,}\s*$',  # 省略号
            r'^\s*\d+\s*$',  # 单独的数字
        ]
        
        # 学术文档结构标识
        self.structure_markers = {
            'title': [r'^[一二三四五六七八九十]+[、．]', r'^\d+[\.、]', r'^第[一二三四五六七八九十]+章'],
            'abstract': ['摘要', 'Abstract', '内容摘要'],
            'keywords': ['关键词', 'Keywords', '主题词'],
            'references': ['参考文献', 'References', '引用文献'],
            'conclusion': ['结论', 'Conclusion', '总结']
        }
    
    def clean_ocr_text(self, text: str) -> str:
        """
        清理OCR识别的文本
        
        Args:
            text: 原始OCR文本
            
        Returns:
            清理后的文本
        """
        if not text:
            return ""
        
        # 1. 基础清理
        cleaned = self._basic_cleanup(text)
        
        # 2. OCR错误修正
        cleaned = self._correct_ocr_errors(cleaned)
        
        # 3. 去除噪声
        cleaned = self._remove_noise(cleaned)
        
        # 4. 标准化格式
        cleaned = self._normalize_format(cleaned)
        
        return cleaned.strip()
    
    def _basic_cleanup(self, text: str) -> str:
        """基础文本清理"""
        # 去除多余空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 去除行首行尾空白
        lines = [line.strip() for line in text.split('\n')]
        
        # 过滤空行
        lines = [line for line in lines if line]
        
        return '\n'.join(lines)
    
    def _correct_ocr_errors(self, text: str) -> str:
        """修正OCR识别错误"""
        corrected = text
        
        # 应用错误映射
        for wrong, correct in self.ocr_corrections.items():
            corrected = corrected.replace(wrong, correct)
        
        # 修正常见的中文OCR错误
        corrections = [
            (r'([a-zA-Z])\s+([a-zA-Z])', r'\1\2'),  # 英文单词间多余空格
            (r'(\d)\s+(\d)', r'\1\2'),  # 数字间多余空格
            (r'([。！？])\s*([a-zA-Z])', r'\1 \2'),  # 中英文间添加空格
        ]
        
        for pattern, replacement in corrections:
            corrected = re.sub(pattern, replacement, corrected)
        
        return corrected
    
    def _remove_noise(self, text: str) -> str:
        """去除噪声内容"""
        lines = text.split('\n')
        filtered_lines = []
        
        for line in lines:
            line = line.strip()
            
            # 检查是否为噪声
            is_noise = False
            for pattern in self.noise_patterns:
                if re.match(pattern, line):
                    is_noise = True
                    break
            
            # 过滤过短的行
            if len(line) < 3:
                is_noise = True
            
            if not is_noise:
                filtered_lines.append(line)
        
        return '\n'.join(filtered_lines)
    
    def _normalize_format(self, text: str) -> str:
        """标准化文本格式"""
        # 标准化段落间距
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # 标准化标点符号
        text = re.sub(r'\s*([，。；：？！])\s*', r'\1', text)
        
        # 标准化引号
        text = re.sub(r'"\s*([^"]*?)\s*"', r'"\1"', text)
        
        return text
    
    def extract_sentences(self, text: str) -> List[str]:
        """提取句子"""
        # 中文句子分割
        sentences = re.split(r'[。！？]', text)
        
        # 清理和过滤句子
        cleaned_sentences = []
        for sentence in sentences:
            sentence = sentence.strip()
            if len(sentence) > 5:  # 过滤过短的句子
                cleaned_sentences.append(sentence)
        
        return cleaned_sentences
    
    def extract_paragraphs(self, text: str) -> List[str]:
        """提取段落"""
        paragraphs = text.split('\n\n')
        
        # 清理段落
        cleaned_paragraphs = []
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if len(paragraph) > 10:  # 过滤过短的段落
                cleaned_paragraphs.append(paragraph)
        
        return cleaned_paragraphs
    
    def identify_structure(self, text: str) -> Dict[str, List[str]]:
        """识别文档结构"""
        structure = {
            'title': [],
            'abstract': [],
            'keywords': [],
            'sections': [],
            'references': [],
            'conclusion': []
        }
        
        lines = text.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 识别结构标记
            section_identified = False
            for section_type, markers in self.structure_markers.items():
                for marker in markers:
                    if isinstance(marker, str):
                        if marker in line:
                            current_section = section_type
                            structure[section_type].append(line)
                            section_identified = True
                            break
                    else:  # 正则表达式
                        if re.match(marker, line):
                            current_section = section_type
                            structure[section_type].append(line)
                            section_identified = True
                            break
                if section_identified:
                    break
            
            # 如果没有识别到新的结构标记，添加到当前section
            if not section_identified and current_section:
                structure[current_section].append(line)
        
        return structure
    
    def calculate_text_quality(self, text: str) -> Dict[str, float]:
        """计算文本质量指标"""
        if not text:
            return {'overall_score': 0.0}
        
        # 基础指标
        total_chars = len(text)
        chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
        english_chars = len(re.findall(r'[a-zA-Z]', text))
        digits = len(re.findall(r'\d', text))
        punctuation = len(re.findall(r'[，。；：？！""''（）]', text))
        
        # 计算各项指标
        chinese_ratio = chinese_chars / total_chars if total_chars > 0 else 0
        english_ratio = english_chars / total_chars if total_chars > 0 else 0
        punctuation_ratio = punctuation / total_chars if total_chars > 0 else 0
        
        # 句子完整性（以句号结尾的比例）
        sentences = self.extract_sentences(text)
        complete_sentences = len([s for s in sentences if s.endswith(('。', '！', '？'))])
        sentence_completeness = complete_sentences / len(sentences) if sentences else 0
        
        # 综合评分
        quality_score = (
            chinese_ratio * 0.4 +  # 中文内容比例
            sentence_completeness * 0.3 +  # 句子完整性
            min(punctuation_ratio * 10, 1.0) * 0.2 +  # 标点符号合理性
            min(total_chars / 1000, 1.0) * 0.1  # 文本长度合理性
        )
        
        return {
            'overall_score': quality_score,
            'chinese_ratio': chinese_ratio,
            'english_ratio': english_ratio,
            'punctuation_ratio': punctuation_ratio,
            'sentence_completeness': sentence_completeness,
            'total_chars': total_chars
        }

def clean_academic_text(text: str) -> str:
    """
    学术文本清理的便捷函数
    
    Args:
        text: 原始文本
        
    Returns:
        清理后的文本
    """
    cleaner = TextCleaner()
    return cleaner.clean_ocr_text(text)

def extract_academic_structure(text: str) -> Dict[str, List[str]]:
    """
    提取学术文档结构的便捷函数
    
    Args:
        text: 文本内容
        
    Returns:
        文档结构字典
    """
    cleaner = TextCleaner()
    return cleaner.identify_structure(text)

# 测试函数
def test_text_cleaner():
    """测试文本清理功能"""
    test_text = """
    张謇与南通女工传习所
    
    1
    
    摘要：本文通过对张謇创办南通女工传习所的历史背景、办学理念、
    教学内容和社会影响的分析，揭示了张謇在近代中国工艺美术教育
    方面的重要贡献。
    
    关键词：张謇；南通女工传习所；工艺美术教育；沈寿
    
    一、引言
    张謇（1853-1926），字季直，号啬庵，江苏南通人。
    
    第2页
    
    二、研究方法
    本研究采用了文献分析法和历史研究法。
    """
    
    cleaner = TextCleaner()
    
    print("原始文本:")
    print(test_text)
    print("\n" + "="*50 + "\n")
    
    # 清理文本
    cleaned = cleaner.clean_ocr_text(test_text)
    print("清理后文本:")
    print(cleaned)
    print("\n" + "="*50 + "\n")
    
    # 提取结构
    structure = cleaner.identify_structure(cleaned)
    print("文档结构:")
    for section, content in structure.items():
        if content:
            print(f"{section}: {content}")
    print("\n" + "="*50 + "\n")
    
    # 质量评估
    quality = cleaner.calculate_text_quality(cleaned)
    print("文本质量:")
    for metric, value in quality.items():
        print(f"{metric}: {value:.3f}")

if __name__ == "__main__":
    test_text_cleaner()