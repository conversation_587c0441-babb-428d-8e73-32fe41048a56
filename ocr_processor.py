#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OCR处理核心模块
提供PDF文档的OCR识别、文本清理和结构化提取功能
"""

import os
import re
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OCRProcessor:
    """OCR处理核心类"""
    
    def __init__(self, output_dir: str = "ocr_results"):
        """
        初始化OCR处理器
        
        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 处理统计
        self.processed_files = []
        self.failed_files = []
        self.processing_stats = {
            'total_files': 0,
            'successful': 0,
            'failed': 0,
            'start_time': None,
            'end_time': None
        }
        
        # 学术关键词库
        self.academic_keywords = {
            '张謇': ['张謇', '张季直', '啬庵'],
            '沈寿': ['沈寿', '沈云芝', '雪宦'],
            '刺绣技艺': ['刺绣', '绣法', '针法', '仿真绣', '苏绣'],
            '教育机构': ['传习所', '女工传习所', '学校', '教育'],
            '工艺特点': ['工艺', '技法', '特点', '风格', '艺术'],
            '文化传承': ['传承', '保护', '发展', '现代化', '创新']
        }
        
        logger.info(f"OCR处理器初始化完成，输出目录: {self.output_dir}")
    
    def pdf_to_images(self, pdf_path: str) -> List[str]:
        """
        将PDF转换为图片
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            图片文件路径列表
        """
        try:
            # 由于环境限制，这里使用模拟实现
            # 在实际环境中，这里会使用pdf2image库
            logger.info(f"模拟PDF转图片: {pdf_path}")
            
            # 模拟生成图片路径
            pdf_name = Path(pdf_path).stem
            image_paths = []
            
            # 假设PDF有多页，生成对应的图片路径
            for page_num in range(1, 6):  # 假设最多5页
                image_path = self.output_dir / f"{pdf_name}_page_{page_num}.png"
                image_paths.append(str(image_path))
                
            logger.info(f"生成 {len(image_paths)} 个图片文件路径")
            return image_paths
            
        except Exception as e:
            logger.error(f"PDF转图片失败: {str(e)}")
            return []
    
    def ocr_recognize(self, image_paths: List[str]) -> List[str]:
        """
        对图片进行OCR识别
        
        Args:
            image_paths: 图片文件路径列表
            
        Returns:
            识别出的文本列表
        """
        try:
            # 由于环境限制，这里使用模拟实现
            # 在实际环境中，这里会使用PaddleOCR
            logger.info(f"模拟OCR识别 {len(image_paths)} 个图片")
            
            recognized_texts = []
            for i, image_path in enumerate(image_paths):
                # 模拟OCR识别结果
                mock_text = f"""
                第{i+1}页内容
                
                标题：学术论文标题
                作者：研究者姓名
                
                摘要：本文通过深入研究，分析了相关问题的核心要点。
                研究发现了重要的学术价值和实践意义。
                
                关键词：学术研究；文献分析；理论探讨
                
                一、引言
                本研究基于对相关领域的深入调研，提出了新的观点和方法。
                
                二、研究方法
                采用了定性和定量相结合的研究方法，确保研究的科学性。
                
                三、研究结果
                通过数据分析，得出了具有重要意义的研究结论。
                """
                recognized_texts.append(mock_text.strip())
                
            logger.info(f"OCR识别完成，共识别 {len(recognized_texts)} 页文本")
            return recognized_texts
            
        except Exception as e:
            logger.error(f"OCR识别失败: {str(e)}")
            return []
    
    def text_cleanup(self, raw_texts: List[str]) -> str:
        """
        清理识别结果，去除噪声
        
        Args:
            raw_texts: 原始识别文本列表
            
        Returns:
            清理后的文本
        """
        try:
            logger.info("开始文本清理")
            
            # 合并所有页面的文本
            combined_text = "\n\n".join(raw_texts)
            
            # 文本清理规则
            cleaned_text = combined_text
            
            # 1. 去除多余的空白字符
            cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
            
            # 2. 修复常见的OCR错误
            ocr_corrections = {
                '0': 'O',  # 数字0误识别为字母O
                '1': 'l',  # 数字1误识别为字母l
                '5': 'S',  # 数字5误识别为字母S
            }
            
            # 3. 去除页眉页脚等噪声
            lines = cleaned_text.split('\n')
            filtered_lines = []
            
            for line in lines:
                line = line.strip()
                if len(line) < 3:  # 过滤过短的行
                    continue
                if re.match(r'^\d+$', line):  # 过滤纯数字行（可能是页码）
                    continue
                if '页码' in line or '第.*页' in line:  # 过滤页码信息
                    continue
                filtered_lines.append(line)
            
            cleaned_text = '\n'.join(filtered_lines)
            
            # 4. 标准化段落格式
            cleaned_text = re.sub(r'\n\s*\n', '\n\n', cleaned_text)
            
            logger.info(f"文本清理完成，原始长度: {len(combined_text)}, 清理后长度: {len(cleaned_text)}")
            return cleaned_text.strip()
            
        except Exception as e:
            logger.error(f"文本清理失败: {str(e)}")
            return ""
    
    def extract_structure(self, cleaned_text: str, filename: str) -> Dict[str, Any]:
        """
        按章节段落组织内容，提取结构化信息
        
        Args:
            cleaned_text: 清理后的文本
            filename: 文件名
            
        Returns:
            结构化的文档信息
        """
        try:
            logger.info(f"开始结构化提取: {filename}")
            
            # 提取基本元数据
            metadata = self._extract_metadata_from_filename(filename)
            
            # 提取文档结构
            structure = self._parse_document_structure(cleaned_text)
            
            # 提取关键词
            keywords = self._extract_keywords(cleaned_text)
            
            # 分类内容
            categories = self._classify_content(metadata['title'], cleaned_text)
            
            # 生成摘要
            abstract = self._generate_abstract(cleaned_text)
            
            # 构建结构化结果
            structured_result = {
                'filename': filename,
                'metadata': metadata,
                'structure': structure,
                'keywords': keywords,
                'categories': categories,
                'abstract': abstract,
                'full_text': cleaned_text,
                'processing_info': {
                    'processed_time': datetime.now().isoformat(),
                    'text_length': len(cleaned_text),
                    'confidence_score': self._calculate_confidence_score(cleaned_text)
                }
            }
            
            logger.info(f"结构化提取完成: {filename}")
            return structured_result
            
        except Exception as e:
            logger.error(f"结构化提取失败: {str(e)}")
            return {}
    
    def _extract_metadata_from_filename(self, filename: str) -> Dict[str, str]:
        """从文件名提取元数据"""
        name_without_ext = filename.replace('.pdf', '')
        parts = name_without_ext.split('_')
        
        if len(parts) > 1:
            title = '_'.join(parts[:-1])
            author = parts[-1]
        else:
            title = name_without_ext
            author = "未知作者"
            
        return {
            'title': title,
            'author': author,
            'filename': filename
        }
    
    def _parse_document_structure(self, text: str) -> Dict[str, List[str]]:
        """解析文档结构"""
        structure = {
            'title': [],
            'abstract': [],
            'keywords': [],
            'sections': [],
            'references': []
        }
        
        lines = text.split('\n')
        current_section = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 识别标题
            if re.match(r'^[一二三四五六七八九十]+[、．]', line):
                current_section = 'sections'
                structure['sections'].append(line)
            elif '摘要' in line or 'Abstract' in line.lower():
                current_section = 'abstract'
                structure['abstract'].append(line)
            elif '关键词' in line or 'Keywords' in line.lower():
                current_section = 'keywords'
                structure['keywords'].append(line)
            elif '参考文献' in line or 'References' in line.lower():
                current_section = 'references'
                structure['references'].append(line)
            elif current_section:
                structure[current_section].append(line)
                
        return structure
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        found_keywords = []
        
        for category, keywords in self.academic_keywords.items():
            for keyword in keywords:
                if keyword in text:
                    found_keywords.append(keyword)
                    
        # 去重并排序
        return sorted(list(set(found_keywords)))
    
    def _classify_content(self, title: str, text: str) -> List[str]:
        """内容分类"""
        categories = []
        
        classification_rules = {
            '工艺特点': ['工艺', '特点', '技法', '针法', '风格'],
            '艺术成就': ['艺术', '成就', '贡献', '价值', '地位'],
            '传承价值': ['传承', '保护', '发展', '现代', '当代'],
            '教育研究': ['教育', '传习所', '学校', '教学', '培养'],
            '历史人物': ['张謇', '沈寿', '人物', '生平', '合作']
        }
        
        content = title + " " + text
        
        for category, keywords in classification_rules.items():
            if any(keyword in content for keyword in keywords):
                categories.append(category)
                
        return categories if categories else ['其他']
    
    def _generate_abstract(self, text: str) -> str:
        """生成摘要"""
        # 简单的摘要生成：取前几句话
        sentences = re.split(r'[。！？]', text)
        abstract_sentences = []
        
        for sentence in sentences[:3]:  # 取前3句
            sentence = sentence.strip()
            if len(sentence) > 10:  # 过滤过短的句子
                abstract_sentences.append(sentence)
                
        return '。'.join(abstract_sentences) + '。' if abstract_sentences else "暂无摘要"
    
    def _calculate_confidence_score(self, text: str) -> float:
        """计算识别置信度分数"""
        # 基于文本质量的简单评分
        score = 0.8  # 基础分数
        
        # 根据文本长度调整
        if len(text) > 1000:
            score += 0.1
        elif len(text) < 100:
            score -= 0.2
            
        # 根据关键词密度调整
        keyword_count = sum(1 for keywords in self.academic_keywords.values() 
                          for keyword in keywords if keyword in text)
        if keyword_count > 5:
            score += 0.1
            
        return min(1.0, max(0.0, score))
    
    def process_single_pdf(self, pdf_path: str) -> Optional[Dict[str, Any]]:
        """
        处理单个PDF文件
        
        Args:
            pdf_path: PDF文件路径
            
        Returns:
            处理结果字典
        """
        try:
            filename = os.path.basename(pdf_path)
            logger.info(f"开始处理PDF文件: {filename}")
            
            # 1. PDF转图片
            image_paths = self.pdf_to_images(pdf_path)
            if not image_paths:
                raise Exception("PDF转图片失败")
            
            # 2. OCR识别
            raw_texts = self.ocr_recognize(image_paths)
            if not raw_texts:
                raise Exception("OCR识别失败")
            
            # 3. 文本清理
            cleaned_text = self.text_cleanup(raw_texts)
            if not cleaned_text:
                raise Exception("文本清理失败")
            
            # 4. 结构化提取
            structured_result = self.extract_structure(cleaned_text, filename)
            if not structured_result:
                raise Exception("结构化提取失败")
            
            # 保存单个文件结果
            output_file = self.output_dir / f"{Path(pdf_path).stem}_ocr_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(structured_result, f, ensure_ascii=False, indent=2)
            
            self.processed_files.append(filename)
            logger.info(f"PDF处理完成: {filename}")
            
            return structured_result
            
        except Exception as e:
            error_msg = f"处理PDF文件失败 {pdf_path}: {str(e)}"
            logger.error(error_msg)
            self.failed_files.append(pdf_path)
            return None
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return {
            'total_files': self.processing_stats['total_files'],
            'successful': len(self.processed_files),
            'failed': len(self.failed_files),
            'success_rate': len(self.processed_files) / max(1, self.processing_stats['total_files']),
            'processed_files': self.processed_files,
            'failed_files': self.failed_files,
            'processing_time': self.processing_stats.get('end_time', datetime.now()) - 
                             self.processing_stats.get('start_time', datetime.now())
        }

def main():
    """测试主函数"""
    processor = OCRProcessor()
    
    # 测试单个文件处理
    test_pdf = "张謇与南通女工传习所_孙佩兰.pdf"
    if os.path.exists(test_pdf):
        result = processor.process_single_pdf(test_pdf)
        if result:
            print("OCR处理测试成功")
            print(f"识别置信度: {result['processing_info']['confidence_score']:.2f}")
        else:
            print("OCR处理测试失败")
    else:
        print(f"测试文件不存在: {test_pdf}")

if __name__ == "__main__":
    main()