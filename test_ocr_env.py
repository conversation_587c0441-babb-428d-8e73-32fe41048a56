#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OCR环境测试脚本
由于网络限制，使用替代方案进行PDF文本提取
"""

import os
import sys
import re
from pathlib import Path

def test_python_environment():
    """测试Python环境"""
    print("=== Python环境测试 ===")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    return True

def test_pdf_files():
    """检查PDF文件"""
    print("\n=== PDF文件检查 ===")
    pdf_files = list(Path(".").glob("*.pdf"))
    print(f"发现PDF文件数量: {len(pdf_files)}")
    
    for i, pdf_file in enumerate(pdf_files, 1):
        file_size = pdf_file.stat().st_size / 1024 / 1024  # MB
        print(f"{i}. {pdf_file.name} ({file_size:.2f} MB)")
    
    return len(pdf_files) > 0

def test_basic_text_processing():
    """测试基本文本处理功能"""
    print("\n=== 基本文本处理测试 ===")
    
    # 测试中文文本处理
    test_text = "张謇与沈寿的艺术合作及其现代意义"
    print(f"测试文本: {test_text}")
    
    # 基本的文本清理
    cleaned_text = re.sub(r'\s+', ' ', test_text).strip()
    print(f"清理后文本: {cleaned_text}")
    
    # 关键词提取（简单版本）
    keywords = ["张謇", "沈寿", "艺术", "合作", "现代意义"]
    found_keywords = [kw for kw in keywords if kw in test_text]
    print(f"发现关键词: {found_keywords}")
    
    return True

def create_mock_ocr_result():
    """创建模拟OCR结果"""
    print("\n=== 创建模拟OCR结果 ===")
    
    mock_result = {
        "filename": "张謇与南通女工传习所_孙佩兰.pdf",
        "pages": 8,
        "extracted_text": """
        张謇与南通女工传习所
        
        摘要：本文通过对张謇创办南通女工传习所的历史背景、办学理念、
        教学内容和社会影响的分析，揭示了张謇在近代中国工艺美术教育
        方面的重要贡献。
        
        关键词：张謇；南通女工传习所；工艺美术教育；沈寿
        
        一、引言
        张謇（1853-1926），字季直，号啬庵，江苏南通人，是中国近代
        著名的实业家、教育家。他在南通创办了多所学校，其中南通女工
        传习所是专门培养刺绣人才的重要机构。
        
        二、南通女工传习所的创办背景
        1904年，张謇在南通创办女工传习所，聘请刺绣大师沈寿担任
        总教习。这一举措不仅为传统刺绣技艺的传承提供了制度保障，
        也为女性接受职业教育开辟了新的道路。
        """,
        "metadata": {
            "author": "孙佩兰",
            "keywords": ["张謇", "南通女工传习所", "工艺美术教育", "沈寿"],
            "abstract": "本文通过对张謇创办南通女工传习所的历史背景、办学理念、教学内容和社会影响的分析，揭示了张謇在近代中国工艺美术教育方面的重要贡献。"
        }
    }
    
    print("模拟OCR结果创建成功")
    print(f"文件名: {mock_result['filename']}")
    print(f"页数: {mock_result['pages']}")
    print(f"作者: {mock_result['metadata']['author']}")
    print(f"关键词: {', '.join(mock_result['metadata']['keywords'])}")
    
    return mock_result

def main():
    """主函数"""
    print("OCR环境测试开始...")
    
    try:
        # 测试Python环境
        test_python_environment()
        
        # 检查PDF文件
        pdf_exists = test_pdf_files()
        
        # 测试文本处理
        test_basic_text_processing()
        
        # 创建模拟结果
        mock_result = create_mock_ocr_result()
        
        print("\n=== 测试总结 ===")
        print("[OK] Python环境正常")
        print(f"[OK] PDF文件检查: {'通过' if pdf_exists else '未发现PDF文件'}")
        print("[OK] 文本处理功能正常")
        print("[OK] 模拟OCR结果生成成功")
        
        print("\n注意：由于网络限制，暂时使用模拟OCR功能")
        print("建议在网络环境改善后安装PaddleOCR等专业OCR工具")
        
        return True
        
    except Exception as e:
        print(f"\n[ERROR] 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)