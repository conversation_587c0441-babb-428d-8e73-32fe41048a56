#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
质量检查脚本
对项目的所有组件进行全面质量检查
"""

import os
import json
from pathlib import Path
from datetime import datetime

class QualityChecker:
    """质量检查器"""
    
    def __init__(self):
        self.check_results = {
            'timestamp': datetime.now().isoformat(),
            'overall_score': 0,
            'checks': {}
        }
    
    def check_file_existence(self):
        """检查关键文件是否存在"""
        required_files = [
            'ocr_processor.py',
            'batch_ocr.py', 
            'content_analyzer.py',
            'report_generator.py',
            'update_html.py',
            'structured_content.json',
            '张赛与沈绣研究综述报告.md',
            '张赛与沈绣论文解读演示.html',
            'ocr_results/processing_report.json'
        ]
        
        missing_files = []
        existing_files = []
        
        for file_path in required_files:
            if Path(file_path).exists():
                existing_files.append(file_path)
            else:
                missing_files.append(file_path)
        
        score = (len(existing_files) / len(required_files)) * 100
        
        self.check_results['checks']['file_existence'] = {
            'score': score,
            'existing_files': existing_files,
            'missing_files': missing_files,
            'total_required': len(required_files)
        }
        
        return score
    
    def check_ocr_results_quality(self):
        """检查OCR结果质量"""
        try:
            with open('ocr_results/processing_report.json', 'r', encoding='utf-8') as f:
                report = json.load(f)
            
            summary = report.get('summary', {})
            quality = report.get('quality_metrics', {})
            
            # 计算质量分数
            success_rate = summary.get('success_rate', 0) * 100
            avg_confidence = quality.get('average_confidence_score', 0) * 100
            
            score = (success_rate + avg_confidence) / 2
            
            self.check_results['checks']['ocr_quality'] = {
                'score': score,
                'success_rate': success_rate,
                'avg_confidence': avg_confidence,
                'total_files': summary.get('total_files', 0),
                'successful_files': summary.get('successful', 0)
            }
            
            return score
            
        except Exception as e:
            self.check_results['checks']['ocr_quality'] = {
                'score': 0,
                'error': str(e)
            }
            return 0
    
    def check_content_analysis_quality(self):
        """检查内容分析质量"""
        try:
            with open('structured_content.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not data:
                score = 0
            else:
                # 检查数据完整性
                complete_papers = 0
                total_papers = len(data)
                
                for paper in data:
                    if (paper.get('metadata') and 
                        paper.get('classification') and 
                        paper.get('keywords') and 
                        paper.get('abstract')):
                        complete_papers += 1
                
                score = (complete_papers / total_papers) * 100 if total_papers > 0 else 0
            
            self.check_results['checks']['content_analysis'] = {
                'score': score,
                'total_papers': len(data),
                'complete_papers': complete_papers,
                'data_integrity': score
            }
            
            return score
            
        except Exception as e:
            self.check_results['checks']['content_analysis'] = {
                'score': 0,
                'error': str(e)
            }
            return 0
    
    def check_report_quality(self):
        """检查报告质量"""
        try:
            report_file = '张赛与沈绣研究综述报告.md'
            if not Path(report_file).exists():
                score = 0
            else:
                with open(report_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查报告结构
                required_sections = ['摘要', '引言', '文献综述', '主题分析', '结论', '参考文献']
                found_sections = []
                
                for section in required_sections:
                    if section in content:
                        found_sections.append(section)
                
                structure_score = (len(found_sections) / len(required_sections)) * 100
                
                # 检查内容长度
                length_score = min(100, len(content) / 50)  # 每50字符1分，最高100分
                
                score = (structure_score + length_score) / 2
            
            self.check_results['checks']['report_quality'] = {
                'score': score,
                'structure_score': structure_score,
                'found_sections': found_sections,
                'content_length': len(content),
                'file_exists': Path(report_file).exists()
            }
            
            return score
            
        except Exception as e:
            self.check_results['checks']['report_quality'] = {
                'score': 0,
                'error': str(e)
            }
            return 0
    
    def check_html_functionality(self):
        """检查HTML页面功能"""
        try:
            html_file = '张赛与沈绣论文解读演示.html'
            if not Path(html_file).exists():
                score = 0
            else:
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查关键组件
                required_components = [
                    'Chart.js',
                    'TailwindCSS', 
                    'Font Awesome',
                    'researchData',
                    'initCharts',
                    'showModal',
                    'generateKeywordCloud'
                ]
                
                found_components = []
                for component in required_components:
                    if component in content:
                        found_components.append(component)
                
                score = (len(found_components) / len(required_components)) * 100
            
            self.check_results['checks']['html_functionality'] = {
                'score': score,
                'found_components': found_components,
                'total_components': len(required_components),
                'file_exists': Path(html_file).exists()
            }
            
            return score
            
        except Exception as e:
            self.check_results['checks']['html_functionality'] = {
                'score': 0,
                'error': str(e)
            }
            return 0
    
    def check_code_quality(self):
        """检查代码质量"""
        python_files = [
            'ocr_processor.py',
            'batch_ocr.py',
            'content_analyzer.py', 
            'report_generator.py',
            'update_html.py'
        ]
        
        total_score = 0
        file_scores = {}
        
        for file_path in python_files:
            if Path(file_path).exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 简单的代码质量检查
                    has_docstring = '"""' in content
                    has_error_handling = 'try:' in content and 'except' in content
                    has_logging = 'logging' in content or 'print(' in content
                    has_main_function = 'def main(' in content
                    
                    file_score = sum([has_docstring, has_error_handling, has_logging, has_main_function]) * 25
                    file_scores[file_path] = file_score
                    total_score += file_score
                    
                except Exception:
                    file_scores[file_path] = 0
            else:
                file_scores[file_path] = 0
        
        avg_score = total_score / len(python_files) if python_files else 0
        
        self.check_results['checks']['code_quality'] = {
            'score': avg_score,
            'file_scores': file_scores,
            'total_files': len(python_files)
        }
        
        return avg_score
    
    def run_all_checks(self):
        """运行所有质量检查"""
        print("开始质量检查...")
        
        checks = [
            ('文件完整性', self.check_file_existence),
            ('OCR结果质量', self.check_ocr_results_quality),
            ('内容分析质量', self.check_content_analysis_quality),
            ('报告质量', self.check_report_quality),
            ('HTML功能性', self.check_html_functionality),
            ('代码质量', self.check_code_quality)
        ]
        
        total_score = 0
        
        for check_name, check_func in checks:
            print(f"检查 {check_name}...")
            score = check_func()
            total_score += score
            print(f"  {check_name}: {score:.1f}/100")
        
        self.check_results['overall_score'] = total_score / len(checks)
        
        print(f"\n总体质量评分: {self.check_results['overall_score']:.1f}/100")
        
        return self.check_results
    
    def save_results(self, filename='quality_check_results.json'):
        """保存检查结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.check_results, f, ensure_ascii=False, indent=2)
        
        print(f"质量检查结果已保存: {filename}")
    
    def generate_summary_report(self):
        """生成质量检查摘要报告"""
        overall_score = self.check_results['overall_score']
        
        if overall_score >= 90:
            grade = "优秀"
            status = "项目质量优秀，所有组件运行良好"
        elif overall_score >= 80:
            grade = "良好" 
            status = "项目质量良好，少数组件需要优化"
        elif overall_score >= 70:
            grade = "合格"
            status = "项目质量合格，部分组件需要改进"
        else:
            grade = "需要改进"
            status = "项目质量需要改进，多个组件存在问题"
        
        summary = f"""
质量检查摘要报告
================

检查时间: {self.check_results['timestamp']}
总体评分: {overall_score:.1f}/100
质量等级: {grade}
项目状态: {status}

详细检查结果:
"""
        
        for check_name, check_result in self.check_results['checks'].items():
            score = check_result.get('score', 0)
            summary += f"- {check_name}: {score:.1f}/100\n"
        
        return summary

def main():
    """主函数"""
    checker = QualityChecker()
    
    # 运行所有检查
    results = checker.run_all_checks()
    
    # 保存结果
    checker.save_results()
    
    # 生成摘要报告
    summary = checker.generate_summary_report()
    print("\n" + summary)
    
    return results['overall_score'] >= 70  # 70分以上认为通过

if __name__ == "__main__":
    success = main()
    print(f"\n质量检查{'通过' if success else '未通过'}")