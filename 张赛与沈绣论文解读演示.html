<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>张赛与沈绣相关论文解读</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.8.0/highlight.min.js"></script>
    <style>
        :root {
            --tech-blue: #00AEEF;
            --tech-green: #4CAF50;
            --highlight-color: var(--tech-blue);
        }
        
        body {
            background: #000000;
            color: #ffffff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .tech-gradient {
            background: linear-gradient(135deg, rgba(0, 174, 239, 0.7), rgba(0, 174, 239, 0.3));
        }
        
        .card-dark {
            background: #1a1a1a;
            border: 1px solid #333;
        }
        
        .mini-card {
            background: #222222;
            border: 1px solid #333;
            transition: all 0.3s ease;
        }
        
        .mini-card:hover {
            transform: translateY(-5px);
            border-color: var(--highlight-color);
            box-shadow: 0 10px 25px rgba(0, 174, 239, 0.2);
        }
        
        .highlight-text {
            color: var(--highlight-color);
        }
        
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }
        
        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
        
        .code-card {
            background: #1e1e1e;
            border: 1px solid #333;
            font-family: 'Courier New', monospace;
        }
        
        .performance-number {
            font-size: 4rem;
            font-weight: bold;
            color: var(--highlight-color);
            line-height: 1;
        }
        
        .tech-icon {
            color: var(--highlight-color);
            font-size: 2rem;
        }
    </style>
</head>
<body>
    <!-- 主标题区域 -->
    <div class="container mx-auto px-6 py-12">
        <div class="text-center mb-16 fade-in">
            <h1 class="text-6xl font-bold mb-4 highlight-text">张赛与沈绣相关论文解读</h1>
            <h2 class="text-2xl text-gray-400">Research Analysis on Zhang Sai and Shen Embroidery</h2>
            <div class="mt-8 w-32 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent mx-auto"></div>
        </div>

        <!-- 项目概述 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <div class="flex items-center mb-6">
                <i class="fas fa-scroll tech-icon mr-4"></i>
                <h2 class="text-3xl font-bold">研究概述 Research Overview</h2>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="mini-card rounded-lg p-6 text-center">
                    <div class="performance-number">15+</div>
                    <div class="text-sm text-gray-400 mt-2">核心论文数量</div>
                    <div class="text-xs text-gray-500">Core Papers</div>
                </div>
                <div class="mini-card rounded-lg p-6 text-center">
                    <div class="performance-number">2006</div>
                    <div class="text-sm text-gray-400 mt-2">最早研究年份</div>
                    <div class="text-xs text-gray-500">Earliest Research</div>
                </div>
                <div class="mini-card rounded-lg p-6 text-center">
                    <div class="performance-number">4+</div>
                    <div class="text-sm text-gray-400 mt-2">主要研究机构</div>
                    <div class="text-xs text-gray-500">Research Institutions</div>
                </div>
            </div>
        </div>

        <!-- 核心研究领域 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <div class="flex items-center mb-6">
                <i class="fas fa-microscope tech-icon mr-4"></i>
                <h2 class="text-3xl font-bold">核心研究领域 Research Areas</h2>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="mini-card rounded-lg p-6">
                    <i class="fas fa-palette text-3xl highlight-text mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">工艺特点研究</h3>
                    <p class="text-sm text-gray-400">深入分析沈绣独特的工艺技法和艺术风格特征</p>
                    <div class="text-xs text-gray-500 mt-2">Craft Characteristics</div>
                </div>
                <div class="mini-card rounded-lg p-6">
                    <i class="fas fa-gem text-3xl highlight-text mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">艺术成就评价</h3>
                    <p class="text-sm text-gray-400">系统梳理沈绣的艺术成就和历史地位</p>
                    <div class="text-xs text-gray-500 mt-2">Artistic Achievement</div>
                </div>
                <div class="mini-card rounded-lg p-6">
                    <i class="fas fa-hands-helping text-3xl highlight-text mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">传承价值研究</h3>
                    <p class="text-sm text-gray-400">探讨沈绣在当代的传承价值和保护意义</p>
                    <div class="text-xs text-gray-500 mt-2">Heritage Value</div>
                </div>
                <div class="mini-card rounded-lg p-6">
                    <i class="fas fa-rocket text-3xl highlight-text mb-4"></i>
                    <h3 class="text-xl font-bold mb-2">现代发展路径</h3>
                    <p class="text-sm text-gray-400">研究沈绣在现代社会的发展和创新方向</p>
                    <div class="text-xs text-gray-500 mt-2">Modern Development</div>
                </div>
            </div>
        </div>

        <!-- 重要论文展示 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <div class="flex items-center mb-6">
                <i class="fas fa-book-open tech-icon mr-4"></i>
                <h2 class="text-3xl font-bold">重要论文 Key Papers</h2>
            </div>
            <div class="space-y-6">
                <div class="mini-card rounded-lg p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div>
                            <h3 class="text-xl font-bold highlight-text">刍议南通沈绣的工艺特点与风格</h3>
                            <p class="text-sm text-gray-400">梁惠娥, 吴敬, 徐亚平 | 丝绸 | 2006</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold highlight-text">4</div>
                            <div class="text-xs text-gray-500">被引次数</div>
                        </div>
                    </div>
                    <p class="text-gray-300">深入分析南通沈绣的独特工艺技法和艺术风格特征，为沈绣研究奠定了重要基础。</p>
                </div>
                
                <div class="mini-card rounded-lg p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div>
                            <h3 class="text-xl font-bold highlight-text">沈绣的艺术成就与传承价值</h3>
                            <p class="text-sm text-gray-400">丁天, 顾森毅 | 南通大学学报 | 2016</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold highlight-text">新</div>
                            <div class="text-xs text-gray-500">研究视角</div>
                        </div>
                    </div>
                    <p class="text-gray-300">系统梳理沈绣的艺术成就，探讨其在当代的传承价值和文化意义。</p>
                </div>
            </div>
        </div>

        <!-- 研究数据可视化 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <div class="flex items-center mb-6">
                <i class="fas fa-chart-bar tech-icon mr-4"></i>
                <h2 class="text-3xl font-bold">研究数据分析 Data Analysis</h2>
            </div>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">论文发表年份分布</h3>
                    <canvas id="yearChart" width="400" height="200"></canvas>
                </div>
                <div>
                    <h3 class="text-xl font-bold mb-4">研究主题分布</h3>
                    <canvas id="topicChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- 技术特点分析 -->
        <div class="card-dark rounded-xl p-8 mb-12 fade-in">
            <div class="flex items-center mb-6">
                <i class="fas fa-cogs tech-icon mr-4"></i>
                <h2 class="text-3xl font-bold">沈绣技术特点 Technical Features</h2>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                <div class="mini-card rounded-lg p-6 text-center">
                    <i class="fas fa-eye text-3xl highlight-text mb-3"></i>
                    <h4 class="text-lg font-bold mb-2">仿真绘画</h4>
                    <p class="text-sm text-gray-400">以仿真绘画为特色，追求逼真的视觉效果</p>
                </div>
                <div class="mini-card rounded-lg p-6 text-center">
                    <i class="fas fa-sun text-3xl highlight-text mb-3"></i>
                    <h4 class="text-lg font-bold mb-2">光影效果</h4>
                    <p class="text-sm text-gray-400">注重光影变化，营造立体感和层次感</p>
                </div>
                <div class="mini-card rounded-lg p-6 text-center">
                    <i class="fas fa-palette text-3xl highlight-text mb-3"></i>
                    <h4 class="text-lg font-bold mb-2">色彩丰富</h4>
                    <p class="text-sm text-gray-400">运用丰富的色彩搭配，表现细腻的色彩变化</p>
                </div>
                <div class="mini-card rounded-lg p-6 text-center">
                    <i class="fas fa-hand-paper text-3xl highlight-text mb-3"></i>
                    <h4 class="text-lg font-bold mb-2">针法精湛</h4>
                    <p class="text-sm text-gray-400">运用多种针法技巧，展现高超的技艺水平</p>
                </div>
            </div>
        </div>

        <!-- 相关链接 -->
        <div class="card-dark rounded-xl p-8 fade-in">
            <div class="flex items-center mb-6">
                <i class="fas fa-link tech-icon mr-4"></i>
                <h2 class="text-3xl font-bold">相关资源链接 Related Links</h2>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <a href="https://www.cnki.net" target="_blank" class="mini-card rounded-lg p-6 hover:bg-gray-800 transition-colors">
                    <i class="fas fa-database text-2xl highlight-text mb-3"></i>
                    <h4 class="font-bold">中国知网</h4>
                    <p class="text-sm text-gray-400">CNKI学术数据库</p>
                </a>
                <a href="http://www.cqvip.com" target="_blank" class="mini-card rounded-lg p-6 hover:bg-gray-800 transition-colors">
                    <i class="fas fa-journal-whills text-2xl highlight-text mb-3"></i>
                    <h4 class="font-bold">维普中文期刊</h4>
                    <p class="text-sm text-gray-400">VIP期刊数据库</p>
                </a>
                <a href="https://scholar.google.com" target="_blank" class="mini-card rounded-lg p-6 hover:bg-gray-800 transition-colors">
                    <i class="fab fa-google text-2xl highlight-text mb-3"></i>
                    <h4 class="font-bold">Google Scholar</h4>
                    <p class="text-sm text-gray-400">谷歌学术搜索</p>
                </a>
                <a href="https://xueshu.baidu.com" target="_blank" class="mini-card rounded-lg p-6 hover:bg-gray-800 transition-colors">
                    <i class="fas fa-search text-2xl highlight-text mb-3"></i>
                    <h4 class="font-bold">百度学术</h4>
                    <p class="text-sm text-gray-400">百度学术搜索</p>
                </a>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.fade-in').forEach(el => {
                observer.observe(el);
            });

            // 初始化图表
            initCharts();
        });

        function initCharts() {
            // 年份分布图表
            const yearCtx = document.getElementById('yearChart').getContext('2d');
            new Chart(yearCtx, {
                type: 'bar',
                data: {
                    labels: ['1993', '2006', '2009', '2014', '2015', '2016', '2017', '2022'],
                    datasets: [{
                        label: '论文数量',
                        data: [1, 1, 1, 1, 4, 1, 1, 1],
                        backgroundColor: 'rgba(0, 174, 239, 0.7)',
                        borderColor: '#00AEEF',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: { color: '#ffffff' }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: { color: '#ffffff' },
                            grid: { color: '#333' }
                        },
                        x: {
                            ticks: { color: '#ffffff' },
                            grid: { color: '#333' }
                        }
                    }
                }
            });

            // 主题分布图表
            const topicCtx = document.getElementById('topicChart').getContext('2d');
            new Chart(topicCtx, {
                type: 'doughnut',
                data: {
                    labels: ['工艺特点', '艺术成就', '传承价值', '现代发展'],
                    datasets: [{
                        data: [35, 25, 25, 15],
                        backgroundColor: [
                            'rgba(0, 174, 239, 0.8)',
                            'rgba(0, 174, 239, 0.6)',
                            'rgba(0, 174, 239, 0.4)',
                            'rgba(0, 174, 239, 0.2)'
                        ],
                        borderColor: '#00AEEF',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: { color: '#ffffff' }
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
