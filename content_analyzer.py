#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
内容分析模块
分析OCR提取的文本内容，进行结构化整理
"""

import os
import json
import re
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

class ContentAnalyzer:
    """内容分析器"""
    
    def __init__(self, ocr_results_dir: str = "ocr_results"):
        self.ocr_results_dir = Path(ocr_results_dir)
        self.structured_data = []
        
        # 主题分类框架
        self.theme_categories = {
            '工艺特点': ['工艺', '技法', '针法', '特点', '风格', '技艺'],
            '艺术成就': ['艺术', '成就', '贡献', '价值', '地位', '影响'],
            '传承价值': ['传承', '保护', '发展', '现代', '当代', '创新'],
            '教育研究': ['教育', '传习所', '学校', '教学', '培养', '研究'],
            '历史人物': ['张謇', '沈寿', '人物', '生平', '合作', '关系']
        }
        
        # 关键词库
        self.keywords_library = {
            '人物': ['张謇', '沈寿', '沈云芝', '雪宦'],
            '机构': ['传习所', '女工传习所', '南通', '学校'],
            '技艺': ['刺绣', '仿真绣', '苏绣', '针法', '工艺'],
            '概念': ['传承', '保护', '发展', '创新', '现代化']
        }
    
    def load_ocr_results(self) -> List[Dict]:
        """加载OCR处理结果"""
        results = []
        json_files = list(self.ocr_results_dir.glob("*_detailed.json"))
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    results.append(data)
            except Exception as e:
                print(f"加载文件失败: {json_file} - {str(e)}")
        
        return results
    
    def extract_metadata(self, ocr_result: Dict) -> Dict:
        """提取论文元信息"""
        metadata = ocr_result.get('metadata', {})
        
        # 提取年份（从标题或内容中）
        year = self._extract_year(ocr_result.get('full_text', ''))
        
        # 提取页数信息
        text_length = len(ocr_result.get('full_text', ''))
        estimated_pages = max(1, text_length // 500)  # 估算页数
        
        return {
            'title': metadata.get('title', ''),
            'author': metadata.get('author', ''),
            'year': year,
            'estimated_pages': estimated_pages,
            'confidence_score': ocr_result.get('processing_info', {}).get('confidence_score', 0),
            'categories': ocr_result.get('categories', []),
            'keywords': ocr_result.get('keywords', [])
        }
    
    def _extract_year(self, text: str) -> str:
        """从文本中提取年份"""
        # 查找四位数年份
        year_pattern = r'(19|20)\d{2}'
        matches = re.findall(year_pattern, text)
        
        if matches:
            # 返回最可能的年份（通常是最早出现的）
            years = [int(m[0] + m[1:]) for m in matches]
            return str(min(years))
        
        return "未知"
    
    def classify_content(self, ocr_result: Dict) -> Dict:
        """按主题分类内容"""
        title = ocr_result.get('metadata', {}).get('title', '')
        content = ocr_result.get('full_text', '')
        combined_text = title + " " + content
        
        classification = {}
        
        for category, keywords in self.theme_categories.items():
            score = 0
            matched_keywords = []
            
            for keyword in keywords:
                count = combined_text.count(keyword)
                if count > 0:
                    score += count
                    matched_keywords.append(keyword)
            
            if score > 0:
                classification[category] = {
                    'score': score,
                    'matched_keywords': matched_keywords,
                    'relevance': min(1.0, score / 10)  # 归一化相关性分数
                }
        
        return classification
    
    def extract_keywords(self, ocr_result: Dict) -> Dict:
        """提取关键词"""
        content = ocr_result.get('full_text', '')
        
        extracted_keywords = {}
        
        for category, keywords in self.keywords_library.items():
            found_keywords = []
            
            for keyword in keywords:
                if keyword in content:
                    count = content.count(keyword)
                    found_keywords.append({
                        'keyword': keyword,
                        'frequency': count
                    })
            
            if found_keywords:
                extracted_keywords[category] = sorted(
                    found_keywords, 
                    key=lambda x: x['frequency'], 
                    reverse=True
                )
        
        return extracted_keywords
    
    def generate_abstract(self, ocr_result: Dict) -> str:
        """生成摘要"""
        content = ocr_result.get('full_text', '')
        
        # 简单的摘要生成：提取关键句子
        sentences = re.split(r'[。！？]', content)
        
        # 选择包含关键词的句子
        key_sentences = []
        for sentence in sentences[:10]:  # 只考虑前10句
            sentence = sentence.strip()
            if len(sentence) > 20:  # 过滤过短的句子
                # 检查是否包含重要关键词
                important_keywords = ['张謇', '沈寿', '传习所', '刺绣', '艺术', '传承']
                if any(keyword in sentence for keyword in important_keywords):
                    key_sentences.append(sentence)
        
        # 组合摘要
        if key_sentences:
            abstract = '。'.join(key_sentences[:3]) + '。'
        else:
            abstract = "本文研究了相关的学术问题，具有重要的理论和实践价值。"
        
        return abstract
    
    def analyze_all_content(self) -> List[Dict]:
        """分析所有内容"""
        ocr_results = self.load_ocr_results()
        
        analyzed_content = []
        
        for ocr_result in ocr_results:
            # 提取元信息
            metadata = self.extract_metadata(ocr_result)
            
            # 内容分类
            classification = self.classify_content(ocr_result)
            
            # 关键词提取
            keywords = self.extract_keywords(ocr_result)
            
            # 生成摘要
            abstract = self.generate_abstract(ocr_result)
            
            # 组合分析结果
            analysis = {
                'filename': ocr_result.get('filename', ''),
                'metadata': metadata,
                'classification': classification,
                'keywords': keywords,
                'abstract': abstract,
                'analysis_time': datetime.now().isoformat()
            }
            
            analyzed_content.append(analysis)
        
        self.structured_data = analyzed_content
        return analyzed_content
    
    def save_structured_content(self, output_file: str = "structured_content.json"):
        """保存结构化内容"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(self.structured_data, f, ensure_ascii=False, indent=2)
        
        print(f"结构化内容已保存: {output_file}")
    
    def generate_summary_report(self) -> Dict:
        """生成汇总报告"""
        if not self.structured_data:
            return {}
        
        # 统计信息
        total_papers = len(self.structured_data)
        
        # 分类统计
        category_stats = {}
        for paper in self.structured_data:
            for category in paper['classification']:
                category_stats[category] = category_stats.get(category, 0) + 1
        
        # 作者统计
        author_stats = {}
        for paper in self.structured_data:
            author = paper['metadata']['author']
            author_stats[author] = author_stats.get(author, 0) + 1
        
        # 年份统计
        year_stats = {}
        for paper in self.structured_data:
            year = paper['metadata']['year']
            year_stats[year] = year_stats.get(year, 0) + 1
        
        return {
            'summary': {
                'total_papers': total_papers,
                'analysis_date': datetime.now().isoformat()
            },
            'category_distribution': dict(sorted(category_stats.items(), key=lambda x: x[1], reverse=True)),
            'author_distribution': dict(sorted(author_stats.items(), key=lambda x: x[1], reverse=True)),
            'year_distribution': dict(sorted(year_stats.items()))
        }

def main():
    """主函数"""
    print("开始内容分析...")
    
    analyzer = ContentAnalyzer()
    
    # 分析所有内容
    analyzed_content = analyzer.analyze_all_content()
    
    # 保存结构化内容
    analyzer.save_structured_content()
    
    # 生成汇总报告
    summary = analyzer.generate_summary_report()
    
    print(f"\n内容分析完成!")
    print(f"分析论文数量: {summary['summary']['total_papers']}")
    print(f"主要分类: {list(summary['category_distribution'].keys())}")
    
    return True

if __name__ == "__main__":
    main()