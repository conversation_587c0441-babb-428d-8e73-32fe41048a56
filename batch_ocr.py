#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
批量OCR处理脚本
批量处理目录中的所有PDF文件，生成结构化的OCR结果
"""

import os
import json
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
import logging

# 导入OCR处理模块
from ocr_processor import OCRProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_ocr.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BatchOCRProcessor:
    """批量OCR处理器"""
    
    def __init__(self, input_dir: str = ".", output_dir: str = "ocr_results"):
        """
        初始化批量处理器
        
        Args:
            input_dir: 输入目录（包含PDF文件）
            output_dir: 输出目录
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 初始化OCR处理器
        self.ocr_processor = OCRProcessor(str(self.output_dir))
        
        # 处理状态
        self.processing_status = {
            'start_time': None,
            'end_time': None,
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'skipped_files': 0,
            'results': [],
            'errors': []
        }
        
        # 断点续传文件
        self.checkpoint_file = self.output_dir / "processing_checkpoint.json"
        
        logger.info(f"批量OCR处理器初始化完成")
        logger.info(f"输入目录: {self.input_dir}")
        logger.info(f"输出目录: {self.output_dir}")
    
    def discover_pdf_files(self) -> List[Path]:
        """发现目录中的PDF文件"""
        pdf_files = list(self.input_dir.glob("*.pdf"))
        logger.info(f"发现 {len(pdf_files)} 个PDF文件")
        
        for i, pdf_file in enumerate(pdf_files, 1):
            file_size = pdf_file.stat().st_size / 1024 / 1024  # MB
            logger.info(f"  {i}. {pdf_file.name} ({file_size:.2f} MB)")
        
        return pdf_files
    
    def load_checkpoint(self) -> Dict[str, Any]:
        """加载断点续传信息"""
        if self.checkpoint_file.exists():
            try:
                with open(self.checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint = json.load(f)
                logger.info(f"加载断点续传信息: 已处理 {len(checkpoint.get('processed_files', []))} 个文件")
                return checkpoint
            except Exception as e:
                logger.warning(f"加载断点续传信息失败: {str(e)}")
        
        return {'processed_files': [], 'failed_files': []}
    
    def save_checkpoint(self):
        """保存断点续传信息"""
        try:
            checkpoint_data = {
                'processed_files': [result['filename'] for result in self.processing_status['results']],
                'failed_files': self.processing_status['errors'],
                'last_update': datetime.now().isoformat()
            }
            
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"保存断点续传信息失败: {str(e)}")
    
    def process_single_file(self, pdf_file: Path) -> Dict[str, Any]:
        """
        处理单个PDF文件
        
        Args:
            pdf_file: PDF文件路径
            
        Returns:
            处理结果
        """
        start_time = time.time()
        filename = pdf_file.name
        
        try:
            logger.info(f"开始处理: {filename}")
            
            # 调用OCR处理器
            result = self.ocr_processor.process_single_pdf(str(pdf_file))
            
            if result:
                processing_time = time.time() - start_time
                result['processing_time_seconds'] = processing_time
                
                # 保存单个文件的详细结果
                output_file = self.output_dir / f"{pdf_file.stem}_detailed.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                
                # 保存纯文本版本
                text_file = self.output_dir / f"{pdf_file.stem}_text.txt"
                with open(text_file, 'w', encoding='utf-8') as f:
                    f.write(result.get('full_text', ''))
                
                logger.info(f"处理完成: {filename} (耗时: {processing_time:.2f}秒)")
                
                return {
                    'filename': filename,
                    'status': 'success',
                    'processing_time': processing_time,
                    'text_length': len(result.get('full_text', '')),
                    'confidence_score': result.get('processing_info', {}).get('confidence_score', 0),
                    'categories': result.get('categories', []),
                    'keywords': result.get('keywords', [])
                }
            else:
                raise Exception("OCR处理返回空结果")
                
        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"处理失败: {filename} - {str(e)}"
            logger.error(error_msg)
            
            return {
                'filename': filename,
                'status': 'failed',
                'processing_time': processing_time,
                'error': str(e)
            }
    
    def calculate_progress_stats(self, current: int, total: int, start_time: float) -> Dict[str, Any]:
        """计算进度统计"""
        elapsed_time = time.time() - start_time
        progress_percent = (current / total) * 100 if total > 0 else 0
        
        if current > 0:
            avg_time_per_file = elapsed_time / current
            estimated_remaining = avg_time_per_file * (total - current)
        else:
            estimated_remaining = 0
        
        return {
            'progress_percent': progress_percent,
            'elapsed_time': elapsed_time,
            'estimated_remaining': estimated_remaining,
            'files_per_minute': (current / elapsed_time) * 60 if elapsed_time > 0 else 0
        }
    
    def process_all_files(self, resume: bool = True) -> Dict[str, Any]:
        """
        批量处理所有PDF文件
        
        Args:
            resume: 是否启用断点续传
            
        Returns:
            处理结果统计
        """
        # 发现PDF文件
        pdf_files = self.discover_pdf_files()
        
        if not pdf_files:
            logger.warning("未发现PDF文件")
            return self.processing_status
        
        # 加载断点续传信息
        checkpoint = self.load_checkpoint() if resume else {'processed_files': [], 'failed_files': []}
        processed_files = set(checkpoint.get('processed_files', []))
        
        # 过滤已处理的文件
        remaining_files = [f for f in pdf_files if f.name not in processed_files]
        
        logger.info(f"总文件数: {len(pdf_files)}")
        logger.info(f"已处理: {len(processed_files)}")
        logger.info(f"待处理: {len(remaining_files)}")
        
        # 初始化处理状态
        self.processing_status.update({
            'start_time': datetime.now().isoformat(),
            'total_files': len(pdf_files),
            'processed_files': len(processed_files),
            'failed_files': 0,
            'skipped_files': len(processed_files)
        })
        
        start_time = time.time()
        
        # 处理每个文件
        for i, pdf_file in enumerate(remaining_files, 1):
            current_total = len(processed_files) + i
            
            # 显示进度
            progress_stats = self.calculate_progress_stats(current_total, len(pdf_files), start_time)
            logger.info(f"进度: {current_total}/{len(pdf_files)} ({progress_stats['progress_percent']:.1f}%) "
                       f"- 预计剩余: {progress_stats['estimated_remaining']/60:.1f}分钟")
            
            # 处理文件
            result = self.process_single_file(pdf_file)
            self.processing_status['results'].append(result)
            
            if result['status'] == 'success':
                self.processing_status['processed_files'] += 1
            else:
                self.processing_status['failed_files'] += 1
                self.processing_status['errors'].append({
                    'filename': result['filename'],
                    'error': result.get('error', 'Unknown error')
                })
            
            # 保存断点续传信息
            self.save_checkpoint()
            
            # 短暂休息，避免系统过载
            time.sleep(0.1)
        
        # 完成处理
        self.processing_status['end_time'] = datetime.now().isoformat()
        
        logger.info("批量处理完成!")
        return self.processing_status
    
    def generate_processing_report(self) -> Dict[str, Any]:
        """生成处理报告"""
        status = self.processing_status
        
        # 计算统计信息
        successful_results = [r for r in status['results'] if r['status'] == 'success']
        failed_results = [r for r in status['results'] if r['status'] == 'failed']
        
        # 分类统计
        category_stats = {}
        keyword_stats = {}
        
        for result in successful_results:
            # 统计分类
            for category in result.get('categories', []):
                category_stats[category] = category_stats.get(category, 0) + 1
            
            # 统计关键词
            for keyword in result.get('keywords', []):
                keyword_stats[keyword] = keyword_stats.get(keyword, 0) + 1
        
        # 质量统计
        confidence_scores = [r.get('confidence_score', 0) for r in successful_results]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        text_lengths = [r.get('text_length', 0) for r in successful_results]
        avg_text_length = sum(text_lengths) / len(text_lengths) if text_lengths else 0
        
        processing_times = [r.get('processing_time', 0) for r in status['results']]
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        report = {
            'summary': {
                'total_files': status['total_files'],
                'successful': len(successful_results),
                'failed': len(failed_results),
                'skipped': status['skipped_files'],
                'success_rate': len(successful_results) / status['total_files'] if status['total_files'] > 0 else 0,
                'start_time': status['start_time'],
                'end_time': status['end_time']
            },
            'quality_metrics': {
                'average_confidence_score': avg_confidence,
                'average_text_length': avg_text_length,
                'average_processing_time': avg_processing_time
            },
            'content_analysis': {
                'category_distribution': dict(sorted(category_stats.items(), key=lambda x: x[1], reverse=True)),
                'top_keywords': dict(sorted(keyword_stats.items(), key=lambda x: x[1], reverse=True)[:20])
            },
            'detailed_results': status['results'],
            'errors': status['errors']
        }
        
        return report
    
    def save_final_report(self, report: Dict[str, Any]):
        """保存最终报告"""
        # 保存JSON格式报告
        report_file = self.output_dir / "processing_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        # 生成可读的文本报告
        text_report_file = self.output_dir / "processing_report.txt"
        with open(text_report_file, 'w', encoding='utf-8') as f:
            f.write("批量OCR处理报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 摘要信息
            summary = report['summary']
            f.write(f"处理摘要:\n")
            f.write(f"- 总文件数: {summary['total_files']}\n")
            f.write(f"- 成功处理: {summary['successful']}\n")
            f.write(f"- 处理失败: {summary['failed']}\n")
            f.write(f"- 跳过文件: {summary['skipped']}\n")
            f.write(f"- 成功率: {summary['success_rate']:.2%}\n")
            f.write(f"- 开始时间: {summary['start_time']}\n")
            f.write(f"- 结束时间: {summary['end_time']}\n\n")
            
            # 质量指标
            quality = report['quality_metrics']
            f.write(f"质量指标:\n")
            f.write(f"- 平均置信度: {quality['average_confidence_score']:.3f}\n")
            f.write(f"- 平均文本长度: {quality['average_text_length']:.0f} 字符\n")
            f.write(f"- 平均处理时间: {quality['average_processing_time']:.2f} 秒\n\n")
            
            # 内容分析
            content = report['content_analysis']
            f.write(f"内容分类统计:\n")
            for category, count in content['category_distribution'].items():
                f.write(f"- {category}: {count} 篇\n")
            
            f.write(f"\n高频关键词:\n")
            for keyword, count in list(content['top_keywords'].items())[:10]:
                f.write(f"- {keyword}: {count} 次\n")
            
            # 错误信息
            if report['errors']:
                f.write(f"\n处理错误:\n")
                for error in report['errors']:
                    f.write(f"- {error['filename']}: {error['error']}\n")
        
        logger.info(f"处理报告已保存: {report_file}")
        logger.info(f"文本报告已保存: {text_report_file}")

def main():
    """主函数"""
    print("开始批量OCR处理...")
    
    # 创建批量处理器
    batch_processor = BatchOCRProcessor()
    
    try:
        # 执行批量处理
        processing_status = batch_processor.process_all_files(resume=True)
        
        # 生成处理报告
        report = batch_processor.generate_processing_report()
        
        # 保存报告
        batch_processor.save_final_report(report)
        
        # 显示摘要
        print("\n" + "=" * 60)
        print("批量OCR处理完成!")
        print("=" * 60)
        print(f"总文件数: {report['summary']['total_files']}")
        print(f"成功处理: {report['summary']['successful']}")
        print(f"处理失败: {report['summary']['failed']}")
        print(f"成功率: {report['summary']['success_rate']:.2%}")
        print(f"平均置信度: {report['quality_metrics']['average_confidence_score']:.3f}")
        
        return True
        
    except Exception as e:
        logger.error(f"批量处理失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)