#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HTML更新脚本
将OCR分析结果集成到HTML展示页面
"""

import json
import re
from pathlib import Path

class HTMLUpdater:
    """HTML更新器"""
    
    def __init__(self, structured_data_file="structured_content.json", 
                 html_file="张赛与沈绣论文解读演示.html"):
        self.structured_data_file = structured_data_file
        self.html_file = html_file
        self.structured_data = self.load_structured_data()
    
    def load_structured_data(self):
        """加载结构化数据"""
        try:
            with open(self.structured_data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载数据失败: {str(e)}")
            return []
    
    def extract_real_data(self):
        """从结构化数据中提取真实统计信息"""
        if not self.structured_data:
            return None
        
        # 统计主题分布
        theme_stats = {}
        for paper in self.structured_data:
            for theme in paper.get('classification', {}):
                theme_stats[theme] = theme_stats.get(theme, 0) + 1
        
        # 统计年份分布
        year_stats = {}
        for paper in self.structured_data:
            year = paper.get('metadata', {}).get('year', '未知')
            year_stats[year] = year_stats.get(year, 0) + 1
        
        # 提取关键词
        keyword_freq = {}
        for paper in self.structured_data:
            for kw_category in paper.get('keywords', {}).values():
                for kw_item in kw_category:
                    keyword = kw_item['keyword']
                    freq = kw_item['frequency']
                    keyword_freq[keyword] = keyword_freq.get(keyword, 0) + freq
        
        # 转换为前端需要的格式
        keywords_list = [
            {'text': kw, 'weight': min(10, freq)} 
            for kw, freq in sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)[:15]
        ]
        
        # 提取重点论文
        papers_list = []
        for paper in self.structured_data[:6]:  # 取前6篇
            metadata = paper.get('metadata', {})
            papers_list.append({
                'title': metadata.get('title', ''),
                'author': metadata.get('author', ''),
                'year': metadata.get('year', '未知'),
                'category': ', '.join(paper.get('classification', {}).keys()),
                'confidence': metadata.get('confidence_score', 0)
            })
        
        return {
            'themes': theme_stats,
            'years': year_stats,
            'keywords': keywords_list,
            'papers': papers_list,
            'total_papers': len(self.structured_data),
            'total_categories': len(theme_stats),
            'avg_confidence': sum(p.get('metadata', {}).get('confidence_score', 0) 
                                for p in self.structured_data) / len(self.structured_data)
        }
    
    def update_html_data(self):
        """更新HTML文件中的数据"""
        real_data = self.extract_real_data()
        if not real_data:
            print("没有可用的数据进行更新")
            return False
        
        try:
            # 读取HTML文件
            with open(self.html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 构建新的JavaScript数据对象
            js_data = f"""
        const researchData = {{
            themes: {json.dumps(real_data['themes'], ensure_ascii=False)},
            years: {json.dumps(real_data['years'], ensure_ascii=False)},
            keywords: {json.dumps(real_data['keywords'], ensure_ascii=False)},
            papers: {json.dumps(real_data['papers'], ensure_ascii=False)}
        }};"""
            
            # 替换HTML中的数据
            pattern = r'const researchData = \{.*?\};'
            html_content = re.sub(pattern, js_data, html_content, flags=re.DOTALL)
            
            # 更新统计数字
            html_content = re.sub(
                r'<div class="stat-number" id="totalPapers">\d+</div>',
                f'<div class="stat-number" id="totalPapers">{real_data["total_papers"]}</div>',
                html_content
            )
            
            html_content = re.sub(
                r'<div class="stat-number" id="totalCategories">\d+</div>',
                f'<div class="stat-number" id="totalCategories">{real_data["total_categories"]}</div>',
                html_content
            )
            
            html_content = re.sub(
                r'<div class="stat-number" id="avgConfidence">\d+%</div>',
                f'<div class="stat-number" id="avgConfidence">{real_data["avg_confidence"]*100:.0f}%</div>',
                html_content
            )
            
            # 保存更新后的HTML
            with open(self.html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"HTML文件已更新: {self.html_file}")
            print(f"- 总文献数: {real_data['total_papers']}")
            print(f"- 主要分类: {real_data['total_categories']}")
            print(f"- 平均置信度: {real_data['avg_confidence']*100:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"更新HTML失败: {str(e)}")
            return False
    
    def create_data_json(self):
        """创建扩展的参考文献数据JSON文件"""
        real_data = self.extract_real_data()
        if not real_data:
            return False
        
        # 创建data目录
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        # 保存扩展数据
        extended_data = {
            'metadata': {
                'total_papers': real_data['total_papers'],
                'total_categories': real_data['total_categories'],
                'avg_confidence': real_data['avg_confidence'],
                'last_updated': '2025-06-23'
            },
            'statistics': {
                'theme_distribution': real_data['themes'],
                'year_distribution': real_data['years'],
                'keyword_frequency': {kw['text']: kw['weight'] for kw in real_data['keywords']}
            },
            'papers': real_data['papers'],
            'detailed_analysis': [
                {
                    'filename': paper.get('filename', ''),
                    'title': paper.get('metadata', {}).get('title', ''),
                    'author': paper.get('metadata', {}).get('author', ''),
                    'categories': list(paper.get('classification', {}).keys()),
                    'keywords': [kw['keyword'] for kw_cat in paper.get('keywords', {}).values() 
                               for kw in kw_cat],
                    'abstract': paper.get('abstract', '')[:200] + '...' if len(paper.get('abstract', '')) > 200 else paper.get('abstract', '')
                }
                for paper in self.structured_data
            ]
        }
        
        output_file = data_dir / "references_extended.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(extended_data, f, ensure_ascii=False, indent=2)
        
        print(f"扩展数据已保存: {output_file}")
        return True

def main():
    """主函数"""
    print("开始更新HTML展示页面...")
    
    updater = HTMLUpdater()
    
    # 更新HTML文件
    if updater.update_html_data():
        print("HTML更新成功!")
    else:
        print("HTML更新失败!")
        return False
    
    # 创建扩展数据文件
    if updater.create_data_json():
        print("数据文件创建成功!")
    else:
        print("数据文件创建失败!")
    
    print("HTML展示页面更新完成!")
    return True

if __name__ == "__main__":
    main()