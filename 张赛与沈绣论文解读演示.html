<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>张赛与沈绣：艺术传承与创新文献解读</title>

    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- highlight.js CDN -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>

    <!-- Chart.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Google Fonts: Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700;900&family=Noto+Sans+SC:wght@400;700;900&display=swap"
        rel="stylesheet">

    <style>
        :root {
            --highlight-color: #00AEEF;
            --highlight-rgb: 0, 174, 239;
            --card-bg: #1a1a1a;
            --border-color: #333333;
        }

        body {
            background-color: #000000;
            color: #e5e7eb;
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            scroll-behavior: smooth;
        }

        .tech-gradient {
            background-image: linear-gradient(to bottom right, rgba(var(--highlight-rgb), 0.1), rgba(var(--highlight-rgb), 0));
        }

        .highlight-text {
            color: var(--highlight-color);
        }

        .main-card {
            background-color: var(--card-bg);
            border: 1px solid var(--border-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .mini-card {
            background-color: #222222;
            border: 1px solid var(--border-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            padding: 1.5rem;
            min-height: 180px;
        }

        .mini-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(var(--highlight-rgb), 0.1);
        }

        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }

        .scroll-animate.is-visible {
            opacity: 1;
            transform: translateY(0);
        }

        .code-card pre {
            background-color: #1a1a1a !important;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border: 1px solid #444;
        }

        .code-card code.hljs {
            color: #c5c8c6;
        }

        .performance-number {
            font-size: 4rem;
            font-weight: bold;
            color: var(--highlight-color);
            line-height: 1;
        }

        /* Modal Styles */
        .modal {
            transition: opacity 0.25s ease;
        }

        .modal-content {
            transition: transform 0.25s ease;
        }
    </style>
</head>

<body class="antialiased">

    <!-- Main Container -->
    <div class="container mx-auto px-4 py-12 md:py-20">

        <!-- Header -->
        <header class="text-center mb-16 md:mb-24 scroll-animate">
            <h1 class="text-4xl md:text-6xl font-black tracking-tight text-white mb-3">张赛与沈绣：艺术传承与创新</h1>
            <p class="text-lg md:text-xl text-gray-400">A Literature Review on Zhang Sai and Shen Xiu Embroidery</p>
        </header>

        <!-- Section 1: Introduction -->
        <section class="mb-16 md:mb-24">
            <div class="main-card rounded-xl p-8 md:p-12 scroll-animate tech-gradient">
                <div class="flex items-center mb-4">
                    <i class="fas fa-book-open-reader text-2xl highlight-text mr-4"></i>
                    <h2 class="text-2xl md:text-3xl font-bold text-white">引言：研究背景与意义</h2>
                </div>
                <p class="text-gray-300 leading-relaxed">
                    清末民初，实业家张謇与刺绣大师沈寿的合作，是中国工艺美术近代化转型的典范。在"实业救国"与"教育救国"思潮下，张謇创办南通女工传习所，为沈寿的"仿真绣"技艺提供了制度性保障与发展平台。此举不仅使刺绣从传统闺阁技艺走向社会化、产业化，更为重要的是，它构建了一个集技艺研究、人才培养、生产销售与国际交流于一体的现代工艺美术生态体系。本报告基于核心期刊文献，系统梳理并解读这一历史性结合的深层内涵、多维影响与宝贵的当代价值。
                </p>
            </div>
        </section>

        <!-- Section 2: Core Findings in Mini-Cards -->
        <section class="mb-16 md:mb-24">
            <h2 class="text-3xl font-bold text-center mb-10 scroll-animate">核心研究发现</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 50ms;">
                    <div class="performance-number">1904</div>
                    <div class="mt-2 text-gray-400 text-sm">南通女工传习所成立</div>
                    <div class="mt-1 text-xs text-gray-500">Nantong Embroidery School Founded</div>
                </div>
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 100ms;">
                    <i class="fas fa-palette text-5xl highlight-text mb-3"></i>
                    <div class="text-2xl font-bold text-white">仿真绣</div>
                    <div class="mt-1 text-gray-400 text-sm">融合西画光影的革命性技法</div>
                    <div class="mt-1 text-xs text-gray-500">"Simulation" Embroidery</div>
                </div>
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 150ms;">
                    <i class="fas fa-industry text-5xl highlight-text mb-3"></i>
                    <div class="text-2xl font-bold text-white">实业兴绣</div>
                    <div class="mt-1 text-gray-400 text-sm">以现代产业模式保育传统工艺</div>
                    <div class="mt-1 text-xs text-gray-500">Industry Revitalizes Craft</div>
                </div>
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 200ms;">
                    <div class="performance-number">1911</div>
                    <div class="mt-2 text-gray-400 text-sm">获都灵万国博览会最优奖</div>
                    <div class="mt-1 text-xs text-gray-500">Turin International Expo, Grand Prize</div>
                </div>
            </div>
        </section>

        <!-- Section 3: Key Quote (Code Snippet Card) -->
        <section class="mb-16 md:mb-24">
            <h2 class="text-3xl font-bold text-center mb-10 scroll-animate">核心文献引述</h2>
            <div class="code-card scroll-animate">
                <div class="main-card rounded-xl p-8">
                    <h3 class="text-xl font-bold text-white mb-4"><i
                            class="fas fa-quote-left highlight-text mr-2"></i>摘自《张謇与南通工艺美术》</h3>
                    <pre><code class="language-markdown">
# 张謇的贡献不仅在于为沈寿提供了资金与场所，
# 更重要的是，他以一位现代思想家的视野，
# 将沈绣从一种个人化的、封闭的技艺，
# 提升到了一个关乎国民生计与文化自信的公共事业高度。
# 这种超越性的认知，是沈绣得以传承并发展的根本动力。
                    </code></pre>
                </div>
            </div>
        </section>

        <!-- Section 4: Technical Features -->
        <section class="mb-16 md:mb-24">
            <h2 class="text-3xl font-bold text-center mb-10 scroll-animate">沈绣技术特点 Technical Features</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 100ms;">
                    <i class="fas fa-eye text-5xl highlight-text mb-3"></i>
                    <div class="text-2xl font-bold text-white">仿真绘画</div>
                    <div class="mt-1 text-gray-400 text-sm">以仿真绘画为特色，追求逼真的视觉效果</div>
                    <div class="mt-1 text-xs text-gray-500">Realistic Painting Style</div>
                </div>
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 200ms;">
                    <i class="fas fa-sun text-5xl highlight-text mb-3"></i>
                    <div class="text-2xl font-bold text-white">光影效果</div>
                    <div class="mt-1 text-gray-400 text-sm">注重光影变化，营造立体感和层次感</div>
                    <div class="mt-1 text-xs text-gray-500">Light & Shadow Effects</div>
                </div>
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 300ms;">
                    <i class="fas fa-palette text-5xl highlight-text mb-3"></i>
                    <div class="text-2xl font-bold text-white">色彩丰富</div>
                    <div class="mt-1 text-gray-400 text-sm">运用丰富的色彩搭配，表现细腻的色彩变化</div>
                    <div class="mt-1 text-xs text-gray-500">Rich Color Palette</div>
                </div>
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 400ms;">
                    <i class="fas fa-hand-paper text-5xl highlight-text mb-3"></i>
                    <div class="text-2xl font-bold text-white">针法精湛</div>
                    <div class="mt-1 text-gray-400 text-sm">运用多种针法技巧，展现高超的技艺水平</div>
                    <div class="mt-1 text-xs text-gray-500">Exquisite Needlework</div>
                </div>
            </div>
        </section>

        <!-- Section 5: Key Works -->
        <section class="mb-16 md:mb-24">
            <h2 class="text-3xl font-bold text-center mb-10 scroll-animate">关键作品分析</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 100ms;">
                    <img src="https://placehold.co/400x300/1a1a1a/00aeef?text=耶稣像" alt="《耶稣像》"
                        class="w-full h-48 object-cover rounded-t-lg mb-4">
                    <h4 class="text-lg font-bold text-white">《耶稣像》</h4>
                    <p class="text-gray-400 text-sm mt-2 px-2">运用上百种色线，通过旋针、虚实针等技法，完美再现了油画的光影层次与人物的悲悯神情，是"仿真绣"的里程碑之作。
                    </p>
                </div>
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 200ms;">
                    <img src="https://placehold.co/400x300/1a1a1a/00aeef?text=意大利皇后像" alt="《意大利皇后像》"
                        class="w-full h-48 object-cover rounded-t-lg mb-4">
                    <h4 class="text-lg font-bold text-white">《意大利皇后像》</h4>
                    <p class="text-gray-400 text-sm mt-2 px-2">作为国礼，其对人物面纱的薄透质感和珠宝的璀璨光泽的精湛表现，震惊欧洲，成功开创了刺绣的国际外交功能。</p>
                </div>
                <div class="mini-card rounded-lg scroll-animate" style="transition-delay: 300ms;">
                    <img src="https://placehold.co/400x300/1a1a1a/00aeef?text=传习所学员作品" alt="传习所学员作品"
                        class="w-full h-48 object-cover rounded-t-lg mb-4">
                    <h4 class="text-lg font-bold text-white">传习所学员作品</h4>
                    <p class="text-gray-400 text-sm mt-2 px-2">这些作品题材广泛，质量稳定，证明了沈寿技艺的可复制性与系统化教学的成功，是技艺得以传承的根本保证。</p>
                </div>
            </div>
        </section>

        <!-- Section 6: Data Visualization -->
        <section class="mb-16 md:mb-24">
            <h2 class="text-3xl font-bold text-center mb-10 scroll-animate">研究数据分析 Data Analysis</h2>
            <div class="main-card rounded-xl p-8 md:p-12 scroll-animate tech-gradient">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div>
                        <h3 class="text-xl font-bold mb-4 text-white">论文发表年份分布</h3>
                        <canvas id="yearChart" width="400" height="200"></canvas>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold mb-4 text-white">研究主题分布</h3>
                        <canvas id="topicChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Section 7: Modern Legacy -->
        <section class="mb-16 md:mb-24">
            <h2 class="text-3xl font-bold text-center mb-10 scroll-animate">现代传承与当代价值</h2>
            <div class="main-card rounded-xl p-8 md:p-12 scroll-animate tech-gradient">
                <div class="flex items-center mb-4">
                    <i class="fas fa-lightbulb text-2xl highlight-text mr-4"></i>
                    <h2 class="text-2xl md:text-3xl font-bold text-white">对当代的启示</h2>
                </div>
                <p class="text-gray-300 leading-relaxed mb-4">
                    <b>"产业+技艺"的非遗保护范本：</b>
                    张謇与沈寿的合作模式，为今日非物质文化遗产的"活态传承"提供了宝贵蓝本。它揭示了单纯的技艺保护是脆弱的，必须与教育、产业、市场相结合，形成良性循环的生态系统，才能使其在现代社会中重获生机。
                </p>
                <p class="text-gray-300 leading-relaxed mb-4">
                    <b>文化IP与品牌建设的先声：</b>
                    张謇通过参与万国博览会、赠送国礼等方式，有意识地将沈绣打造为国家级的文化名片。这种早期"品牌出海"的战略思维，对当今中国文化品牌走向世界，利用文化IP发展文创产业具有重要的指导意义。
                </p>
                <p class="text-gray-300 leading-relaxed">
                    <b>跨界融合的设计创新源泉：</b>
                    "仿真绣"本身就是传统刺绣与西方绘画艺术跨界融合的产物。其对光影、色彩、透视的探索，持续为当代服装设计、家纺艺术、数字媒体艺术等领域提供灵感，是设计创新中"中西合璧，古为今用"的绝佳案例。
                </p>
            </div>
        </section>

        <!-- Section 8: References -->
        <section>
            <h2 class="text-3xl font-bold text-center mb-10 scroll-animate">核心参考文献</h2>
            <div id="references-list" class="main-card rounded-xl p-8 md:p-12 scroll-animate tech-gradient">
                <!-- References will be dynamically inserted here -->
            </div>
        </section>
    </div>

    <!-- Modal Structure -->
    <div id="abstract-modal"
        class="modal fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75 hidden opacity-0">
        <div
            class="modal-content bg-gray-900 border border-gray-700 rounded-xl shadow-xl w-full max-w-2xl mx-auto p-6 md:p-8 transform scale-95">
            <div class="flex justify-between items-center border-b border-gray-700 pb-3 mb-4">
                <h3 id="modal-title" class="text-xl font-bold highlight-text"></h3>
                <button id="modal-close-btn" class="text-gray-400 hover:text-white text-2xl">&times;</button>
            </div>
            <div id="modal-body" class="text-gray-300 leading-relaxed max-h-[60vh] overflow-y-auto pr-2">
                <p id="modal-source" class="font-semibold mb-2"></p>
                <p id="modal-keywords" class="text-sm text-gray-400 mb-4"></p>
                <p id="modal-abstract"></p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Highlight.js Initialization
            hljs.highlightAll();

            // --- Reference Data ---
            const references = [
                {
                    title: "张謇与沈寿的艺术合作及其现代意义",
                    author: "冯梅",
                    year: "2022",
                    journal: "中国国家博物馆馆刊",
                    keywords: "张謇；沈寿；沈绣；南通女工传习所；工艺美术",
                    abstract: "张謇与沈寿的合作是近代中国工艺美术转型的重要案例。张謇以其现代企业家的视野与社会改革家的胸怀，为沈寿的"仿真绣"艺术提供了制度性、产业化的发展平台。文章论述了张謇通过创办女工传习所、建立销售渠道、参与国际赛会等方式，将沈绣从个人技艺提升为社会事业的过程，并探讨了该合作模式对当代非物质文化遗产保护与文化产业发展的启示。",
                    cnki_url: "https://kns.cnki.net/kns/search?sfield=kw&skey=张謇与沈寿的艺术合作及其现代意义",
                    vip_url: "http://www.cqvip.com/qk/search.aspx?key=张謇与沈寿的艺术合作及其现代意义"
                },
                {
                    title: "从南通女工传习所看清末民初的工艺美术教育",
                    author: "李欣",
                    year: "2020",
                    journal: "装饰",
                    keywords: "工艺美术教育；南通女工传习所；张謇；沈寿；女子教育",
                    abstract: "南通女工传习所不仅是刺绣人才的培养基地，更是近代中国工艺美术教育的重要实验。本文通过分析传习所的教学模式、课程设置和人才培养成果，揭示了张謇在工艺美术教育方面的前瞻性思考。传习所将技艺传授与文化教育相结合，既保持了传统工艺的精髓，又融入了现代教育理念，为中国工艺美术教育的现代化转型提供了宝贵经验。",
                    cnki_url: "https://kns.cnki.net/kns/search?sfield=kw&skey=从南通女工传习所看清末民初的工艺美术教育",
                    vip_url: "http://www.cqvip.com/qk/search.aspx?key=从南通女工传习所看清末民初的工艺美术教育"
                }
            ];

            // --- Populate References & Setup Modals ---
            const referencesList = document.getElementById('references-list');
            const modal = document.getElementById('abstract-modal');
            const modalTitle = document.getElementById('modal-title');
            const modalSource = document.getElementById('modal-source');
            const modalKeywords = document.getElementById('modal-keywords');
            const modalAbstract = document.getElementById('modal-abstract');
            const modalCloseBtn = document.getElementById('modal-close-btn');

            references.forEach((ref, index) => {
                const li = document.createElement('li');
                li.className = 'text-gray-300 hover:text-white transition-colors duration-300 pb-2 border-b border-gray-800 mb-4';
                li.innerHTML = `
                    <div class="flex justify-between items-center">
                        <div class="flex-1">
                            <i class="fas fa-book-open highlight-text mr-3"></i>
                            <span>${ref.author}. (${ref.year}). ${ref.title}. <span class="font-bold text-sm highlight-text">[${ref.journal}]</span>.</span>
                        </div>
                        <div class="flex items-center ml-4">
                            <button data-index="${index}" class="view-abstract-btn bg-gray-700 hover:bg-gray-600 text-xs font-bold py-1 px-3 rounded-md transition-colors duration-200">查看摘要</button>
                            <a href="${ref.cnki_url}" target="_blank" class="ml-2 text-gray-400 hover:highlight-text" title="在知网中搜索"><i class="fas fa-search"></i></a>
                        </div>
                    </div>
                `;
                referencesList.appendChild(li);
            });

            const openModal = (index) => {
                const ref = references[index];
                modalTitle.textContent = ref.title;
                modalSource.textContent = `来源: ${ref.author}, ${ref.journal}, ${ref.year}`;
                modalKeywords.textContent = `关键词: ${ref.keywords}`;
                modalAbstract.textContent = ref.abstract;
                modal.classList.remove('hidden');
                setTimeout(() => {
                    modal.classList.remove('opacity-0');
                    modal.querySelector('.modal-content').classList.remove('scale-95');
                }, 10);
            };

            const closeModal = () => {
                modal.classList.add('opacity-0');
                modal.querySelector('.modal-content').classList.add('scale-95');
                setTimeout(() => modal.classList.add('hidden'), 250);
            };

            document.querySelectorAll('.view-abstract-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    openModal(btn.dataset.index);
                });
            });

            modalCloseBtn.addEventListener('click', closeModal);
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // --- Intersection Observer for animations ---
            const animatedElements = document.querySelectorAll('.scroll-animate');

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('is-visible');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '0px',
                threshold: 0.1
            });

            animatedElements.forEach(el => observer.observe(el));

            function initCharts() {
                // 年份分布图表
                const yearCtx = document.getElementById('yearChart').getContext('2d');
                new Chart(yearCtx, {
                    type: 'bar',
                    data: {
                        labels: ['1993', '2006', '2009', '2014', '2015', '2016', '2017', '2022'],
                        datasets: [{
                            label: '论文数量',
                            data: [1, 1, 1, 1, 4, 1, 1, 1],
                            backgroundColor: 'rgba(0, 174, 239, 0.7)',
                            borderColor: '#00AEEF',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                labels: { color: '#ffffff' }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: { color: '#ffffff' },
                                grid: { color: '#333' }
                            },
                            x: {
                                ticks: { color: '#ffffff' },
                                grid: { color: '#333' }
                            }
                        }
                    }
                });

                // 主题分布图表
                const topicCtx = document.getElementById('topicChart').getContext('2d');
                new Chart(topicCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['工艺特点', '艺术成就', '传承价值', '现代发展'],
                        datasets: [{
                            data: [35, 25, 25, 15],
                            backgroundColor: [
                                'rgba(0, 174, 239, 0.8)',
                                'rgba(0, 174, 239, 0.6)',
                                'rgba(0, 174, 239, 0.4)',
                                'rgba(0, 174, 239, 0.2)'
                            ],
                            borderColor: '#00AEEF',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                labels: { color: '#ffffff' }
                            }
                        }
                    }
                });
            }

            // 初始化图表
            initCharts();
        });
    </script>
</body>

</html>